defmodule Drops.Relation.SQL.Introspector.SpecialCasesTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Introspector

  describe "SQLite special cases" do
    @tag relations: [:special_cases]
    test "detects foreign key relationships", %{repo: repo} do
      columns = Introspector.introspect_table_columns(repo, "special_cases")

      # Find foreign key columns
      user_id_col = find_column(columns, "user_id")
      parent_id_col = find_column(columns, "parent_id")

      # Verify they are detected as integer types (foreign keys are typically integers)
      assert Introspector.db_type_to_ecto_type(repo, user_id_col.type, user_id_col.name) ==
               :integer

      assert Introspector.db_type_to_ecto_type(
               repo,
               parent_id_col.type,
               parent_id_col.name
             ) == :integer
    end

    @tag relations: [:special_cases]
    test "handles nullable vs non-nullable columns", %{repo: repo} do
      columns = Introspector.introspect_table_columns(repo, "special_cases")

      # Find nullable and non-nullable columns
      required_col = find_column(columns, "required_field")
      optional_col = find_column(columns, "optional_field")

      # Verify null constraints are detected correctly
      assert required_col.not_null == true
      assert optional_col.not_null == false
    end

    @tag relations: [:special_cases]
    test "handles columns with default values", %{repo: repo} do
      columns = Introspector.introspect_table_columns(repo, "special_cases")

      # Find columns with defaults
      default_string_col = find_column(columns, "default_string")
      default_integer_col = find_column(columns, "default_integer")
      default_boolean_col = find_column(columns, "default_boolean")

      # Verify types are correctly mapped regardless of defaults
      assert Introspector.db_type_to_ecto_type(
               repo,
               default_string_col.type,
               default_string_col.name
             ) == :string

      assert Introspector.db_type_to_ecto_type(
               repo,
               default_integer_col.type,
               default_integer_col.name
             ) == :integer

      assert Introspector.db_type_to_ecto_type(
               repo,
               default_boolean_col.type,
               default_boolean_col.name
             ) == :boolean
    end

    test "SQLite boolean field naming convention detection" do
      repo = Drops.TestRepo

      # Test various boolean naming patterns
      boolean_patterns = [
        "is_active",
        "has_permission",
        "can_edit",
        "should_process",
        "will_expire",
        "enabled_flag",
        "disabled_flag",
        "active_flag",
        "inactive_flag",
        "visible_flag",
        "hidden_flag"
      ]

      Enum.each(boolean_patterns, fn field_name ->
        assert Introspector.db_type_to_ecto_type(repo, "INTEGER", field_name) == :boolean,
               "Field #{field_name} should be detected as boolean"
      end)

      # Test non-boolean patterns
      non_boolean_patterns = [
        "user_id",
        "count",
        "total",
        "amount",
        "id",
        "parent_id",
        "order_number"
      ]

      Enum.each(non_boolean_patterns, fn field_name ->
        assert Introspector.db_type_to_ecto_type(repo, "INTEGER", field_name) == :integer,
               "Field #{field_name} should not be detected as boolean"
      end)
    end

    test "SQLite type affinity handling" do
      repo = Drops.TestRepo

      # Test that SQLite's flexible typing is handled correctly
      # Even with unusual type declarations, we should map to sensible Ecto types
      assert Introspector.db_type_to_ecto_type(repo, "INT", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "STRING", "name") == :string
      assert Introspector.db_type_to_ecto_type(repo, "FLOAT", "price") == :float
      assert Introspector.db_type_to_ecto_type(repo, "UNKNOWN_TYPE", "field") == :string
    end

    test "SQLite case insensitive type matching" do
      repo = Drops.TestRepo

      # Test that type matching is case insensitive
      assert Introspector.db_type_to_ecto_type(repo, "integer", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "INTEGER", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "Integer", "id") == :integer

      assert Introspector.db_type_to_ecto_type(repo, "text", "name") == :string
      assert Introspector.db_type_to_ecto_type(repo, "TEXT", "name") == :string
      assert Introspector.db_type_to_ecto_type(repo, "Text", "name") == :string
    end
  end

  describe "PostgreSQL special cases" do
    @tag relations: [:postgres_array_types], adapter: :postgres
    test "handles complex array type parsing", %{repo: repo} do
      # Test nested array parsing and complex type combinations
      assert Introspector.db_type_to_ecto_type(repo, "integer[]", "numbers") ==
               {:array, :integer}

      assert Introspector.db_type_to_ecto_type(repo, "character varying[]", "strings") ==
               {:array, :string}

      assert Introspector.db_type_to_ecto_type(
               repo,
               "timestamp with time zone[]",
               "timestamps"
             ) == {:array, :utc_datetime}

      assert Introspector.db_type_to_ecto_type(repo, "uuid[]", "identifiers") ==
               {:array, :binary_id}
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "handles PostgreSQL type aliases correctly", %{repo: repo} do
      # Test that PostgreSQL type aliases are mapped correctly
      assert Introspector.db_type_to_ecto_type(repo, "int2", "small_num") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "int4", "medium_num") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "int8", "big_num") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "float4", "small_float") == :float
      assert Introspector.db_type_to_ecto_type(repo, "float8", "big_float") == :float
      assert Introspector.db_type_to_ecto_type(repo, "timetz", "time_with_tz") == :time

      assert Introspector.db_type_to_ecto_type(repo, "timestamptz", "timestamp_with_tz") ==
               :utc_datetime
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "handles PostgreSQL parameterized types", %{repo: repo} do
      # Test parameterized types are handled correctly
      assert Introspector.db_type_to_ecto_type(
               repo,
               "character varying(255)",
               "limited_string"
             ) == :string

      assert Introspector.db_type_to_ecto_type(repo, "varchar(100)", "short_string") ==
               :string

      assert Introspector.db_type_to_ecto_type(repo, "character(10)", "fixed_string") ==
               :string

      assert Introspector.db_type_to_ecto_type(repo, "char(5)", "code") == :string

      assert Introspector.db_type_to_ecto_type(repo, "numeric(10,2)", "precise_decimal") ==
               :decimal

      assert Introspector.db_type_to_ecto_type(repo, "decimal(15,4)", "money_amount") ==
               :decimal
    end

    @tag relations: [:postgres_types], adapter: :postgres
    test "handles PostgreSQL case sensitivity", %{repo: repo} do
      # PostgreSQL type names should be case insensitive in our mapping
      assert Introspector.db_type_to_ecto_type(repo, "INTEGER", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "integer", "id") == :integer
      assert Introspector.db_type_to_ecto_type(repo, "TEXT", "content") == :string
      assert Introspector.db_type_to_ecto_type(repo, "text", "content") == :string
      assert Introspector.db_type_to_ecto_type(repo, "BOOLEAN", "flag") == :boolean
      assert Introspector.db_type_to_ecto_type(repo, "boolean", "flag") == :boolean
    end

    test "PostgreSQL range type detection" do
      repo = Drops.TestRepo

      # Test that all range types are detected correctly
      range_types = [
        "int4range",
        "int8range",
        "numrange",
        "tsrange",
        "tstzrange",
        "daterange"
      ]

      Enum.each(range_types, fn range_type ->
        assert Introspector.db_type_to_ecto_type(repo, range_type, "range_field") ==
                 :string,
               "Range type #{range_type} should be mapped to string"
      end)
    end

    test "PostgreSQL geometric type detection" do
      repo = Drops.TestRepo

      # Test that all geometric types are detected correctly
      geometric_types = [
        "point",
        "line",
        "lseg",
        "box",
        "path",
        "polygon",
        "circle"
      ]

      Enum.each(geometric_types, fn geo_type ->
        assert Introspector.db_type_to_ecto_type(repo, geo_type, "geo_field") == :string,
               "Geometric type #{geo_type} should be mapped to string"
      end)
    end
  end

  describe "Cross-database compatibility" do
    test "unknown types default to string" do
      repo = Drops.TestRepo

      # Test that unknown types default to string for both adapters
      unknown_types = [
        "CUSTOM_TYPE",
        "WEIRD_TYPE",
        "UNKNOWN",
        "MADE_UP_TYPE"
      ]

      Enum.each(unknown_types, fn unknown_type ->
        assert Introspector.db_type_to_ecto_type(repo, unknown_type, "field") == :string,
               "Unknown type #{unknown_type} should default to string"
      end)
    end

    test "handles empty and nil type strings gracefully" do
      repo = Drops.TestRepo

      # Test edge cases with empty or unusual type strings
      assert Introspector.db_type_to_ecto_type(repo, "", "field") == :string
      assert Introspector.db_type_to_ecto_type(repo, " ", "field") == :string
    end
  end

  # Helper function to find a column by name
  defp find_column(columns, name) do
    Enum.find(columns, &(&1.name == name)) ||
      raise "Column #{name} not found in #{inspect(Enum.map(columns, & &1.name))}"
  end
end
